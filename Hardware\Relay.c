#include "stm32f10x.h"     // Device header
#include "Relay.h"
#include "Config.h"
#include "Delay.h"

// 继电器状态变量
static uint8_t relay_status = 0x00;        // 继电器状态寄存器（位0-3对应通道0-3）
static RelayMode_t relay_mode = RELAY_MODE_SINGLE;  // 默认单通道模式

/**
  * 函    数：继电器初始化
  * 参    数：无
  * 返 回 值：无
  */
void Relay_Init(void)
{
    /*开启时钟*/
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);  // 开启GPIOC的时钟
    
    /*GPIO初始化*/
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;        // 推挽输出模式
    GPIO_InitStructure.GPIO_Pin = RELAY_PIN_0 | RELAY_PIN_1 | RELAY_PIN_2 | RELAY_PIN_3;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOC, &GPIO_InitStructure);                  // 将PC0-PC3引脚初始化为推挽输出
    
    /*设置GPIO初始化后的默认电平*/
    GPIO_ResetBits(GPIOC, RELAY_PIN_0 | RELAY_PIN_1 | RELAY_PIN_2 | RELAY_PIN_3);  // 设置PC0-PC3引脚为低电平（继电器关闭）
    
    /*初始化状态变量*/
    relay_status = 0x00;                                    // 所有继电器关闭
    relay_mode = RELAY_MODE_SINGLE;                         // 默认单通道模式
}

/**
  * 函    数：设置指定通道状态
  * 参    数：channel 继电器通道，state 继电器状态
  * 返 回 值：无
  */
void Relay_SetChannel(RelayChannel_t channel, RelayState_t state)
{
    uint16_t pin_mask = 0;
    
    // 参数检查
    if (channel > RELAY_CHANNEL_3) {
        return;  // 无效通道
    }
    
    // 获取对应的引脚掩码
    switch (channel) {
        case RELAY_CHANNEL_0: pin_mask = RELAY_PIN_0; break;
        case RELAY_CHANNEL_1: pin_mask = RELAY_PIN_1; break;
        case RELAY_CHANNEL_2: pin_mask = RELAY_PIN_2; break;
        case RELAY_CHANNEL_3: pin_mask = RELAY_PIN_3; break;
        default: return;
    }
    
    // 单通道模式下，开启新通道前先关闭所有其他通道
    if (relay_mode == RELAY_MODE_SINGLE && state == RELAY_ON) {
        Relay_AllOff();
        Delay_ms(RELAY_SWITCH_DELAY);  // 继电器切换延时
    }
    
    // 设置继电器状态
    if (state == RELAY_ON) {
        GPIO_SetBits(GPIOC, pin_mask);          // 设置引脚为高电平（继电器开启）
        relay_status |= (1 << channel);         // 更新状态寄存器
    } else {
        GPIO_ResetBits(GPIOC, pin_mask);        // 设置引脚为低电平（继电器关闭）
        relay_status &= ~(1 << channel);        // 更新状态寄存器
    }
    
    // 继电器动作稳定延时
    Delay_ms(RELAY_SWITCH_DELAY);
}

/**
  * 函    数：关闭所有继电器
  * 参    数：无
  * 返 回 值：无
  */
void Relay_AllOff(void)
{
    /*设置所有继电器引脚为低电平*/
    GPIO_ResetBits(GPIOC, RELAY_PIN_0 | RELAY_PIN_1 | RELAY_PIN_2 | RELAY_PIN_3);
    
    /*更新状态寄存器*/
    relay_status = 0x00;
    
    /*继电器动作稳定延时*/
    Delay_ms(RELAY_SWITCH_DELAY);
}

/**
  * 函    数：获取指定通道状态
  * 参    数：channel 继电器通道
  * 返 回 值：继电器状态
  */
RelayState_t Relay_GetStatus(RelayChannel_t channel)
{
    // 参数检查
    if (channel > RELAY_CHANNEL_3) {
        return RELAY_OFF;  // 无效通道返回关闭状态
    }
    
    // 从状态寄存器读取状态
    return (relay_status & (1 << channel)) ? RELAY_ON : RELAY_OFF;
}

/**
  * 函    数：获取所有通道状态
  * 参    数：无
  * 返 回 值：所有通道状态的位掩码（位0-3对应通道0-3）
  */
uint8_t Relay_GetAllStatus(void)
{
    return relay_status;
}

/**
  * 函    数：设置工作模式
  * 参    数：mode 工作模式
  * 返 回 值：无
  */
void Relay_SetMode(RelayMode_t mode)
{
    relay_mode = mode;
    
    // 如果切换到单通道模式且当前有多个通道开启，则关闭所有通道
    if (mode == RELAY_MODE_SINGLE) {
        uint8_t active_count = 0;
        for (int i = 0; i < 4; i++) {
            if (relay_status & (1 << i)) {
                active_count++;
            }
        }
        if (active_count > 1) {
            Relay_AllOff();
        }
    }
}

/**
  * 函    数：获取当前工作模式
  * 参    数：无
  * 返 回 值：当前工作模式
  */
RelayMode_t Relay_GetMode(void)
{
    return relay_mode;
}

/**
  * 函    数：翻转指定通道状态
  * 参    数：channel 继电器通道
  * 返 回 值：无
  */
void Relay_Toggle(RelayChannel_t channel)
{
    RelayState_t current_state = Relay_GetStatus(channel);
    RelayState_t new_state = (current_state == RELAY_ON) ? RELAY_OFF : RELAY_ON;
    Relay_SetChannel(channel, new_state);
}

/**
  * 函    数：继电器自检功能
  * 参    数：无
  * 返 回 值：自检结果（0=失败，1=成功）
  */
uint8_t Relay_SelfTest(void)
{
    uint8_t test_result = 1;  // 默认测试成功
    
    // 保存当前状态和模式
    uint8_t original_status = relay_status;
    RelayMode_t original_mode = relay_mode;
    
    // 设置为多通道模式进行测试
    Relay_SetMode(RELAY_MODE_MULTI);
    
    // 测试每个通道的开启和关闭
    for (RelayChannel_t ch = RELAY_CHANNEL_0; ch <= RELAY_CHANNEL_3; ch++) {
        // 测试开启
        Relay_SetChannel(ch, RELAY_ON);
        if (Relay_GetStatus(ch) != RELAY_ON) {
            test_result = 0;  // 测试失败
            break;
        }
        
        // 测试关闭
        Relay_SetChannel(ch, RELAY_OFF);
        if (Relay_GetStatus(ch) != RELAY_OFF) {
            test_result = 0;  // 测试失败
            break;
        }
    }
    
    // 测试全部关闭功能
    Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
    Relay_SetChannel(RELAY_CHANNEL_1, RELAY_ON);
    Relay_AllOff();
    if (Relay_GetAllStatus() != 0x00) {
        test_result = 0;  // 测试失败
    }
    
    // 恢复原始状态和模式
    Relay_SetMode(original_mode);
    relay_status = 0x00;  // 先清零
    Relay_AllOff();       // 确保所有继电器关闭
    
    // 恢复原始状态
    for (int i = 0; i < 4; i++) {
        if (original_status & (1 << i)) {
            Relay_SetChannel((RelayChannel_t)i, RELAY_ON);
        }
    }
    
    return test_result;
}
