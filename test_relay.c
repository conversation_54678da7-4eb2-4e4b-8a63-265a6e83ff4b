/**
 * 继电器控制模块测试程序
 * 用于验证继电器控制功能的正确性
 */

#include "stm32f10x.h"
#include "Relay.h"
#include "Delay.h"
#include "Serial.h"

/**
 * 继电器功能测试
 */
void Test_Relay_Functions(void)
{
    Serial_SendString("=== 继电器控制模块测试开始 ===\r\n");
    
    // 1. 初始化测试
    Serial_SendString("1. 继电器初始化测试...\r\n");
    Relay_Init();
    Serial_SendString("   初始化完成\r\n");
    
    // 2. 单通道开关测试
    Serial_SendString("2. 单通道开关测试...\r\n");
    for (RelayChannel_t ch = RELAY_CHANNEL_0; ch <= RELAY_CHANNEL_3; ch++) {
        Serial_Printf("   测试通道%d开启...\r\n", ch);
        Relay_SetChannel(ch, RELAY_ON);
        Delay_ms(500);
        
        if (Relay_GetStatus(ch) == RELAY_ON) {
            Serial_Printf("   通道%d开启成功\r\n", ch);
        } else {
            Serial_Printf("   通道%d开启失败\r\n", ch);
        }
        
        Serial_Printf("   测试通道%d关闭...\r\n", ch);
        Relay_SetChannel(ch, RELAY_OFF);
        Delay_ms(500);
        
        if (Relay_GetStatus(ch) == RELAY_OFF) {
            Serial_Printf("   通道%d关闭成功\r\n", ch);
        } else {
            Serial_Printf("   通道%d关闭失败\r\n", ch);
        }
    }
    
    // 3. 工作模式测试
    Serial_SendString("3. 工作模式测试...\r\n");
    
    // 测试单通道模式
    Serial_SendString("   测试单通道模式...\r\n");
    Relay_SetMode(RELAY_MODE_SINGLE);
    Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
    Relay_SetChannel(RELAY_CHANNEL_1, RELAY_ON);  // 应该自动关闭通道0
    
    if (Relay_GetStatus(RELAY_CHANNEL_0) == RELAY_OFF && 
        Relay_GetStatus(RELAY_CHANNEL_1) == RELAY_ON) {
        Serial_SendString("   单通道模式测试成功\r\n");
    } else {
        Serial_SendString("   单通道模式测试失败\r\n");
    }
    
    // 测试多通道模式
    Serial_SendString("   测试多通道模式...\r\n");
    Relay_SetMode(RELAY_MODE_MULTI);
    Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
    Relay_SetChannel(RELAY_CHANNEL_1, RELAY_ON);
    
    if (Relay_GetStatus(RELAY_CHANNEL_0) == RELAY_ON && 
        Relay_GetStatus(RELAY_CHANNEL_1) == RELAY_ON) {
        Serial_SendString("   多通道模式测试成功\r\n");
    } else {
        Serial_SendString("   多通道模式测试失败\r\n");
    }
    
    // 4. 全部关闭测试
    Serial_SendString("4. 全部关闭功能测试...\r\n");
    Relay_AllOff();
    
    if (Relay_GetAllStatus() == 0x00) {
        Serial_SendString("   全部关闭功能测试成功\r\n");
    } else {
        Serial_SendString("   全部关闭功能测试失败\r\n");
    }
    
    // 5. 自检功能测试
    Serial_SendString("5. 自检功能测试...\r\n");
    uint8_t self_test_result = Relay_SelfTest();
    
    if (self_test_result == 1) {
        Serial_SendString("   自检功能测试成功\r\n");
    } else {
        Serial_SendString("   自检功能测试失败\r\n");
    }
    
    // 6. 切换时序测试
    Serial_SendString("6. 切换时序测试...\r\n");
    uint32_t start_time, end_time;
    
    start_time = SysTick->VAL;
    Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
    end_time = SysTick->VAL;
    
    // 计算切换时间（简化计算）
    uint32_t switch_time = (start_time > end_time) ? 
                          (start_time - end_time) : 
                          (0xFFFFFF - end_time + start_time);
    
    Serial_Printf("   通道切换时间: %d 系统时钟周期\r\n", switch_time);
    
    if (switch_time < 7200000) {  // 假设100ms对应的时钟周期数
        Serial_SendString("   切换时序测试成功（≤100ms）\r\n");
    } else {
        Serial_SendString("   切换时序测试失败（>100ms）\r\n");
    }
    
    Serial_SendString("=== 继电器控制模块测试完成 ===\r\n\r\n");
}

/**
 * 继电器状态显示
 */
void Display_Relay_Status(void)
{
    uint8_t status = Relay_GetAllStatus();
    RelayMode_t mode = Relay_GetMode();
    
    Serial_Printf("继电器状态: 0x%02X [", status);
    for (int i = 0; i < 4; i++) {
        Serial_Printf("%s", (status & (1 << i)) ? "ON " : "OFF ");
    }
    Serial_Printf("] 模式: %s\r\n", 
                  (mode == RELAY_MODE_SINGLE) ? "单通道" : "多通道");
}
