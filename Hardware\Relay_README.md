# 继电器控制模块使用说明

## 概述
继电器控制模块用于控制四路继电器，实现多通道样品切换功能。支持单通道和多通道工作模式，提供继电器状态监控和故障检测功能。

## 硬件连接
- **继电器通道0**: PC0 (GPIO推挽输出)
- **继电器通道1**: PC1 (GPIO推挽输出)  
- **继电器通道2**: PC2 (GPIO推挽输出)
- **继电器通道3**: PC3 (GPIO推挽输出)

## 主要功能

### 1. 基本控制功能
- `Relay_Init()`: 继电器初始化
- `Relay_SetChannel()`: 设置指定通道状态
- `Relay_AllOff()`: 关闭所有继电器
- `Relay_GetStatus()`: 获取指定通道状态
- `Relay_GetAllStatus()`: 获取所有通道状态

### 2. 工作模式
- **单通道模式**: 同时只能开启一个通道，适用于互斥切换
- **多通道模式**: 可同时开启多个通道，适用于并行测试

### 3. 高级功能
- `Relay_Toggle()`: 翻转指定通道状态
- `Relay_SelfTest()`: 继电器自检功能
- 状态监控和故障检测

## 使用示例

```c
#include "Relay.h"

int main(void)
{
    // 初始化继电器
    Relay_Init();
    
    // 设置为单通道模式
    Relay_SetMode(RELAY_MODE_SINGLE);
    
    // 开启通道0
    Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
    
    // 检查状态
    if (Relay_GetStatus(RELAY_CHANNEL_0) == RELAY_ON) {
        // 通道0已开启
    }
    
    // 切换到通道1（自动关闭通道0）
    Relay_SetChannel(RELAY_CHANNEL_1, RELAY_ON);
    
    // 关闭所有继电器
    Relay_AllOff();
    
    // 执行自检
    if (Relay_SelfTest() == 1) {
        // 自检通过
    }
    
    return 0;
}
```

## 配置参数

在 `Config.h` 中定义的相关参数：

```c
// 继电器控制配置
#define RELAY_PIN_0            GPIO_Pin_0   // 继电器通道0控制引脚 PC0
#define RELAY_PIN_1            GPIO_Pin_1   // 继电器通道1控制引脚 PC1
#define RELAY_PIN_2            GPIO_Pin_2   // 继电器通道2控制引脚 PC2
#define RELAY_PIN_3            GPIO_Pin_3   // 继电器通道3控制引脚 PC3
#define RELAY_SWITCH_DELAY     20           // 继电器切换延时 20ms
#define RELAY_SETTLE_TIME      50           // 继电器稳定时间 50ms
```

## 技术特性

- **切换时间**: ≤100ms
- **稳定延时**: 20ms（可配置）
- **驱动能力**: 推挽输出，50MHz
- **状态监控**: 实时状态寄存器
- **故障检测**: 自检功能
- **工作模式**: 单通道/多通道可切换

## 注意事项

1. **继电器驱动**: 根据继电器线圈电流需求，可能需要添加三极管驱动电路
2. **切换延时**: 继电器切换需要适当延时确保触点稳定，避免测量干扰
3. **电源要求**: 确保继电器驱动电路有足够的电源供应
4. **EMI防护**: 继电器切换可能产生电磁干扰，建议添加滤波电路
5. **机械寿命**: 继电器有机械寿命限制，避免频繁切换

## 故障排除

1. **继电器不动作**: 检查GPIO配置和电源供应
2. **切换时间过长**: 检查延时配置和硬件响应
3. **状态读取错误**: 检查状态寄存器更新逻辑
4. **自检失败**: 检查硬件连接和继电器工作状态

## 扩展功能

模块设计支持以下扩展：
- 增加更多继电器通道
- 添加继电器状态反馈检测
- 实现继电器寿命监控
- 支持不同类型的继电器驱动
