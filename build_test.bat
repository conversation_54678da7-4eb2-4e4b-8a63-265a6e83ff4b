@echo off
echo STM32温度采集系统编译测试
echo ================================

echo 检查项目文件...
if exist "Project.uvprojx" (
    echo ✓ 项目文件存在
) else (
    echo ✗ 项目文件不存在
    goto :error
)

echo 检查源文件...
if exist "User\main.c" (
    echo ✓ main.c存在
) else (
    echo ✗ main.c不存在
    goto :error
)

if exist "Hardware\AD.c" (
    echo ✓ AD.c存在
) else (
    echo ✗ AD.c不存在
    goto :error
)

if exist "Hardware\Serial.c" (
    echo ✓ Serial.c存在
) else (
    echo ✗ Serial.c不存在
    goto :error
)

if exist "Hardware\OLED.c" (
    echo ✓ OLED.c存在
) else (
    echo ✗ OLED.c不存在
    goto :error
)

echo 检查头文件...
if exist "Hardware\Config.h" (
    echo ✓ Config.h存在
) else (
    echo ✗ Config.h不存在
    goto :error
)

echo.
echo ================================
echo 所有文件检查完成！
echo 项目结构正确，可以进行编译。
echo.
echo 编译步骤：
echo 1. 打开Keil uVision5
echo 2. 打开Project.uvprojx文件
echo 3. 点击编译按钮
echo 4. 下载到STM32开发板
echo.
echo 使用说明：
echo 1. 连接LM35温度传感器到PA0-PA3
echo 2. 连接串口到PA9(TX)和PA10(RX)
echo 3. 连接OLED到PB8(SCL)和PB9(SDA)
echo 4. 运行test_temperature.py接收数据
echo ================================
goto :end

:error
echo.
echo ================================
echo 错误：项目文件不完整！
echo 请检查文件是否正确创建。
echo ================================

:end
pause
