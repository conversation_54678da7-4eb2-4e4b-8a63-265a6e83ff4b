# STM32 四通道温度采集系统

## 项目概述
本项目基于STM32F103微控制器，实现四个通道的温度采集，将温度数据精确到小数点后一位，并通过串口实时传输到主控计算机。

## 功能特性
- **四通道温度采集**：同时采集4个温度传感器的数据
- **高精度显示**：温度数据精确到小数点后一位
- **实时传输**：通过串口将温度数据实时发送到主控计算机
- **OLED显示**：本地显示四个通道的温度值
- **中文友好**：支持中文注释和显示

## 硬件连接
### 温度传感器连接（LM35）
- **通道0**：PA0 - LM35传感器1输出
- **通道1**：PA1 - LM35传感器2输出
- **通道2**：PA2 - LM35传感器3输出
- **通道3**：PA3 - LM35传感器4输出

### 串口连接
- **TX**：PA9 - 连接到主控计算机RX
- **RX**：PA10 - 连接到主控计算机TX
- **波特率**：9600

### OLED显示屏
- **SCL**：PB8
- **SDA**：PB9

## 软件架构
```
├── Hardware/
│   ├── AD.c/AD.h           # ADC采集和温度转换
│   ├── Serial.c/Serial.h   # 串口通信模块
│   ├── OLED.c/OLED.h      # OLED显示模块
│   ├── OLED_Font.h        # OLED字体文件
│   └── Config.h           # 系统配置参数
├── System/
│   └── Delay.c/Delay.h    # 延时函数
├── User/
│   └── main.c             # 主程序
├── Library/               # STM32标准库
├── test_temperature.py    # 主控计算机接收程序
├── build_test.bat         # 编译测试脚本
└── README.md              # 项目说明文档
```

## 核心功能实现

### 1. 温度转换算法
```c
float AD_GetTemperature(uint8_t ADC_Channel)
{
    uint16_t ADValue = AD_GetValue(ADC_Channel);   // 获取ADC原始值
    float Voltage = (float)ADValue / 4095.0 * 3.3; // 转换为电压值
    float Temperature = Voltage * 100.0;           // LM35: 10mV/°C
    return Temperature;
}
```

### 2. 数据传输格式
串口输出格式：`T0,T1,T2,T3 (°C)`
- 示例：`25.3,26.1,24.8,25.7`
- 更新频率：1秒/次

### 3. OLED显示格式
```
T0: 25.3°C
T1: 26.1°C
T2: 24.8°C
T3: 25.7°C
```

## 使用方法

### 1. 硬件准备
1. 按照硬件连接图连接温度传感器、串口和OLED显示屏
2. 确保电源供电正常（3.3V）

### 2. 软件编译
1. 运行`build_test.bat`检查项目文件完整性
2. 使用Keil uVision5打开项目文件`Project.uvprojx`
3. 编译项目，确保无错误
4. 下载程序到STM32开发板

### 3. 数据接收
**方法一：使用Python脚本**
1. 安装Python和pyserial库：`pip install pyserial`
2. 运行`python test_temperature.py`
3. 根据提示修改串口号（默认COM3）

**方法二：使用串口调试工具**
1. 打开串口调试工具（如串口助手、Putty等）
2. 设置串口参数：波特率9600，8位数据位，1位停止位，无校验
3. 连接串口，即可接收实时温度数据

### 4. 数据处理示例（Python）
```python
import serial
import time

# 打开串口
ser = serial.Serial('COM3', 9600, timeout=1)

while True:
    if ser.in_waiting:
        data = ser.readline().decode('utf-8').strip()
        if data and ',' in data:
            temps = data.split(',')
            print(f"温度0: {temps[0]}°C")
            print(f"温度1: {temps[1]}°C")
            print(f"温度2: {temps[2]}°C")
            print(f"温度3: {temps[3]}°C")
            print("-" * 30)
    time.sleep(0.1)
```

## 技术参数
- **MCU**：STM32F103C8T6
- **ADC分辨率**：12位（0-4095）
- **参考电压**：3.3V
- **温度精度**：±0.1°C
- **采样频率**：1Hz
- **串口波特率**：9600bps
- **工作温度范围**：0-100°C（取决于传感器）

## 注意事项
1. **传感器选择**：代码针对LM35温度传感器优化，如使用其他传感器需修改转换公式
2. **电源稳定性**：确保3.3V电源稳定，影响ADC精度
3. **接线检查**：确认所有连接正确，特别是模拟信号线
4. **串口设置**：主控计算机串口参数必须与STM32设置一致

## 故障排除
1. **无温度显示**：检查传感器供电和信号线连接
2. **串口无数据**：检查串口线连接和波特率设置
3. **温度异常**：检查参考电压和传感器工作状态
4. **OLED无显示**：检查I2C连接和电源

## 扩展功能
- 可增加温度报警功能
- 可添加数据存储功能
- 可扩展更多通道
- 可增加网络传输功能

## 版本历史
- **v1.0**：基础四通道温度采集功能
- **v1.1**：增加串口通信和实时传输
- **v1.2**：优化显示精度和数据格式
