/**
 * 命令解析模块测试程序
 * 用于验证CommandParser模块的功能正确性
 */

#include "stm32f10x.h"
#include "CommandParser.h"
#include "Serial.h"
#include "Relay.h"
#include "Delay.h"
#include <string.h>

/**
 * 模拟串口接收数据
 */
void Simulate_SerialReceive(const char* command)
{
    for (int i = 0; command[i] != '\0'; i++) {
        // 模拟串口中断接收
        extern uint8_t Serial_RxData;
        extern uint8_t Serial_RxFlag;
        
        Serial_RxData = command[i];
        Serial_RxFlag = 1;
        
        // 处理命令
        CommandParser_ProcessCommand();
        
        Delay_ms(1); // 模拟字符间隔
    }
}

/**
 * 测试继电器控制命令
 */
void Test_RelayCommands(void)
{
    Serial_SendString("=== 测试继电器控制命令 ===\r\n");
    
    // 测试通道开启命令
    Serial_SendString("测试: #RELAY:CH0_ON\r\n");
    Simulate_SerialReceive("#RELAY:CH0_ON\r\n");
    Delay_ms(100);
    
    Serial_SendString("测试: #RELAY:CH1_ON\r\n");
    Simulate_SerialReceive("#RELAY:CH1_ON\r\n");
    Delay_ms(100);
    
    // 测试通道关闭命令
    Serial_SendString("测试: #RELAY:CH0_OFF\r\n");
    Simulate_SerialReceive("#RELAY:CH0_OFF\r\n");
    Delay_ms(100);
    
    // 测试全部关闭命令
    Serial_SendString("测试: #RELAY:ALL_OFF\r\n");
    Simulate_SerialReceive("#RELAY:ALL_OFF\r\n");
    Delay_ms(100);
    
    Serial_SendString("继电器控制命令测试完成\r\n\r\n");
}

/**
 * 测试状态查询命令
 */
void Test_StatusCommands(void)
{
    Serial_SendString("=== 测试状态查询命令 ===\r\n");
    
    // 先设置一些继电器状态
    Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
    Relay_SetChannel(RELAY_CHANNEL_2, RELAY_ON);
    
    // 测试全部状态查询
    Serial_SendString("测试: #STATUS:GET\r\n");
    Simulate_SerialReceive("#STATUS:GET\r\n");
    Delay_ms(100);
    
    // 测试单个通道状态查询
    Serial_SendString("测试: #STATUS:CH0\r\n");
    Simulate_SerialReceive("#STATUS:CH0\r\n");
    Delay_ms(100);
    
    Serial_SendString("测试: #STATUS:CH1\r\n");
    Simulate_SerialReceive("#STATUS:CH1\r\n");
    Delay_ms(100);
    
    Serial_SendString("状态查询命令测试完成\r\n\r\n");
}

/**
 * 测试模式切换命令
 */
void Test_ModeCommands(void)
{
    Serial_SendString("=== 测试模式切换命令 ===\r\n");
    
    // 测试设置单通道模式
    Serial_SendString("测试: #MODE:SINGLE\r\n");
    Simulate_SerialReceive("#MODE:SINGLE\r\n");
    Delay_ms(100);
    
    // 测试设置多通道模式
    Serial_SendString("测试: #MODE:MULTI\r\n");
    Simulate_SerialReceive("#MODE:MULTI\r\n");
    Delay_ms(100);
    
    // 测试查询当前模式
    Serial_SendString("测试: #MODE:GET\r\n");
    Simulate_SerialReceive("#MODE:GET\r\n");
    Delay_ms(100);
    
    Serial_SendString("模式切换命令测试完成\r\n\r\n");
}

/**
 * 测试自检命令
 */
void Test_TestCommands(void)
{
    Serial_SendString("=== 测试自检命令 ===\r\n");
    
    // 测试继电器自检
    Serial_SendString("测试: #TEST:RELAY\r\n");
    Simulate_SerialReceive("#TEST:RELAY\r\n");
    Delay_ms(500); // 自检需要更多时间
    
    Serial_SendString("自检命令测试完成\r\n\r\n");
}

/**
 * 测试系统信息命令
 */
void Test_SystemCommands(void)
{
    Serial_SendString("=== 测试系统信息命令 ===\r\n");
    
    // 测试系统信息查询
    Serial_SendString("测试: #SYSTEM:INFO\r\n");
    Simulate_SerialReceive("#SYSTEM:INFO\r\n");
    Delay_ms(100);
    
    // 测试版本查询
    Serial_SendString("测试: #SYSTEM:VERSION\r\n");
    Simulate_SerialReceive("#SYSTEM:VERSION\r\n");
    Delay_ms(100);
    
    Serial_SendString("系统信息命令测试完成\r\n\r\n");
}

/**
 * 测试错误处理
 */
void Test_ErrorHandling(void)
{
    Serial_SendString("=== 测试错误处理 ===\r\n");
    
    // 测试无效命令
    Serial_SendString("测试: #INVALID:CMD\r\n");
    Simulate_SerialReceive("#INVALID:CMD\r\n");
    Delay_ms(100);
    
    // 测试无效参数
    Serial_SendString("测试: #RELAY:INVALID_PARAM\r\n");
    Simulate_SerialReceive("#RELAY:INVALID_PARAM\r\n");
    Delay_ms(100);
    
    // 测试格式错误
    Serial_SendString("测试: RELAY:CH0_ON (缺少#)\r\n");
    Simulate_SerialReceive("RELAY:CH0_ON\r\n");
    Delay_ms(100);
    
    Serial_SendString("错误处理测试完成\r\n\r\n");
}

/**
 * 测试命令解析性能
 */
void Test_Performance(void)
{
    Serial_SendString("=== 测试命令解析性能 ===\r\n");
    
    uint32_t start_time, end_time, total_time;
    const int test_count = 100;
    
    start_time = SysTick->VAL;
    
    for (int i = 0; i < test_count; i++) {
        Simulate_SerialReceive("#STATUS:GET\r\n");
    }
    
    end_time = SysTick->VAL;
    
    // 计算总时间（简化计算）
    total_time = (start_time > end_time) ? 
                 (start_time - end_time) : 
                 (0xFFFFFF - end_time + start_time);
    
    Serial_Printf("处理%d个命令总时间: %d 系统时钟周期\r\n", test_count, total_time);
    Serial_Printf("平均每个命令处理时间: %d 系统时钟周期\r\n", total_time / test_count);
    
    Serial_SendString("性能测试完成\r\n\r\n");
}

/**
 * 命令解析模块完整测试
 */
void Test_CommandParser_Complete(void)
{
    Serial_SendString("=== 命令解析模块完整测试开始 ===\r\n\r\n");
    
    // 初始化模块
    CommandParser_Init();
    Relay_Init();
    
    // 执行各项测试
    Test_RelayCommands();
    Test_StatusCommands();
    Test_ModeCommands();
    Test_TestCommands();
    Test_SystemCommands();
    Test_ErrorHandling();
    Test_Performance();
    
    Serial_SendString("=== 命令解析模块完整测试完成 ===\r\n");
}

/**
 * 交互式命令测试
 */
void Test_Interactive_Commands(void)
{
    Serial_SendString("=== 交互式命令测试模式 ===\r\n");
    Serial_SendString("请发送命令进行测试，发送 #SYSTEM:INFO 查看系统信息\r\n");
    Serial_SendString("支持的命令格式:\r\n");
    Serial_SendString("  #RELAY:CH0_ON/CH0_OFF/CH1_ON/CH1_OFF/CH2_ON/CH2_OFF/CH3_ON/CH3_OFF/ALL_OFF\r\n");
    Serial_SendString("  #STATUS:GET/CH0/CH1/CH2/CH3\r\n");
    Serial_SendString("  #MODE:SINGLE/MULTI/GET\r\n");
    Serial_SendString("  #TEST:RELAY\r\n");
    Serial_SendString("  #SYSTEM:INFO/VERSION\r\n");
    Serial_SendString("开始监听命令...\r\n\r\n");
    
    while (1) {
        if (CommandParser_ProcessCommand()) {
            // 命令处理成功，可以添加额外的处理逻辑
        }
        Delay_ms(10); // 避免CPU占用过高
    }
}
