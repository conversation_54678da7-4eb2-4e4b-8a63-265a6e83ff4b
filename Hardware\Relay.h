#ifndef __RELAY_H
#define __RELAY_H

#include <stdint.h>

// 继电器通道定义
typedef enum {
    RELAY_CHANNEL_0 = 0,    // 继电器通道0
    RELAY_CHANNEL_1 = 1,    // 继电器通道1
    RELAY_CHANNEL_2 = 2,    // 继电器通道2
    RELAY_CHANNEL_3 = 3,    // 继电器通道3
    RELAY_CHANNEL_ALL = 4   // 所有继电器通道
} RelayChannel_t;

// 继电器状态定义
typedef enum {
    RELAY_OFF = 0,          // 继电器关闭
    RELAY_ON = 1            // 继电器开启
} RelayState_t;

// 继电器工作模式定义
typedef enum {
    RELAY_MODE_SINGLE = 0,  // 单通道模式（同时只能开启一个通道）
    RELAY_MODE_MULTI = 1    // 多通道模式（可同时开启多个通道）
} RelayMode_t;

// 函数声明
void Relay_Init(void);                                          // 继电器初始化
void Relay_SetChannel(RelayChannel_t channel, RelayState_t state); // 设置指定通道状态
void Relay_AllOff(void);                                        // 关闭所有继电器
RelayState_t Relay_GetStatus(RelayChannel_t channel);           // 获取指定通道状态
uint8_t Relay_GetAllStatus(void);                              // 获取所有通道状态（位掩码）
void Relay_SetMode(RelayMode_t mode);                           // 设置工作模式
RelayMode_t Relay_GetMode(void);                                // 获取当前工作模式
void Relay_Toggle(RelayChannel_t channel);                     // 翻转指定通道状态
uint8_t Relay_SelfTest(void);                                  // 继电器自检功能

#endif
