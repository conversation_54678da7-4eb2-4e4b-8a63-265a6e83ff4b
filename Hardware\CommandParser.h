#ifndef __COMMAND_PARSER_H
#define __COMMAND_PARSER_H

#include <stdint.h>
#include <string.h>

// 命令类型定义
typedef enum {
    CMD_TYPE_RELAY = 0,     // 继电器控制命令
    CMD_TYPE_STATUS = 1,    // 状态查询命令
    CMD_TYPE_MODE = 2,      // 模式切换命令
    CMD_TYPE_TEST = 3,      // 自检命令
    CMD_TYPE_SYSTEM = 4,    // 系统信息命令
    CMD_TYPE_UNKNOWN = 5    // 未知命令
} CommandType_t;

// 命令解析状态定义
typedef enum {
    PARSE_STATE_IDLE = 0,       // 空闲状态
    PARSE_STATE_START = 1,      // 开始解析
    PARSE_STATE_CMD = 2,        // 解析命令
    PARSE_STATE_PARAM = 3,      // 解析参数
    PARSE_STATE_END = 4,        // 解析完成
    PARSE_STATE_ERROR = 5       // 解析错误
} ParseState_t;

// 响应状态定义
typedef enum {
    RESPONSE_OK = 0,            // 命令执行成功
    RESPONSE_ERROR = 1          // 命令执行失败
} ResponseStatus_t;

// 命令结构体定义
typedef struct {
    CommandType_t type;         // 命令类型
    char command[16];           // 命令字符串
    char parameter[32];         // 参数字符串
    uint8_t valid;              // 命令有效标志
} Command_t;

// 函数声明
void CommandParser_Init(void);                              // 命令解析器初始化
uint8_t CommandParser_ProcessCommand(void);                // 处理接收到的命令
void CommandParser_SendResponse(ResponseStatus_t status, const char* data); // 发送响应
void CommandParser_ParseBuffer(const char* buffer, Command_t* cmd); // 解析命令缓冲区
CommandType_t CommandParser_GetCommandType(const char* cmd_str); // 获取命令类型
uint8_t CommandParser_ExecuteCommand(const Command_t* cmd); // 执行命令
void CommandParser_HandleRelayCommand(const Command_t* cmd); // 处理继电器命令
void CommandParser_HandleStatusCommand(const Command_t* cmd); // 处理状态查询命令
void CommandParser_HandleModeCommand(const Command_t* cmd); // 处理模式切换命令
void CommandParser_HandleTestCommand(const Command_t* cmd); // 处理自检命令
void CommandParser_HandleSystemCommand(const Command_t* cmd); // 处理系统信息命令
uint8_t CommandParser_ValidateCommand(const Command_t* cmd); // 验证命令有效性

#endif
