#include "stm32f10x.h"     // Device header
#include "CommandParser.h"
#include "Serial.h"
#include "Relay.h"
#include "Config.h"
#include <stdio.h>
#include <stdlib.h>

// 命令缓冲区和状态变量
static char command_buffer[CMD_BUFFER_SIZE];    // 命令接收缓冲区
static uint8_t buffer_index = 0;               // 缓冲区索引
static ParseState_t parse_state = PARSE_STATE_IDLE; // 解析状态
static uint32_t last_receive_time = 0;         // 最后接收时间（用于超时检测）

/**
  * 函    数：命令解析器初始化
  * 参    数：无
  * 返 回 值：无
  */
void CommandParser_Init(void)
{
    // 清空命令缓冲区
    memset(command_buffer, 0, CMD_BUFFER_SIZE);
    buffer_index = 0;
    parse_state = PARSE_STATE_IDLE;
    last_receive_time = 0;
}

/**
  * 函    数：处理接收到的命令
  * 参    数：无
  * 返 回 值：处理结果（1=成功处理命令，0=无命令或处理失败）
  */
uint8_t CommandParser_ProcessCommand(void)
{
    uint8_t received_data;
    Command_t command;
    
    // 检查是否有串口数据接收
    if (Serial_GetRxFlag()) {
        received_data = Serial_GetRxData();
        
        // 检查缓冲区是否溢出
        if (buffer_index >= CMD_BUFFER_SIZE - 1) {
            // 缓冲区溢出，重置状态
            CommandParser_Init();
            CommandParser_SendResponse(RESPONSE_ERROR, "BUFFER_OVERFLOW");
            return 0;
        }
        
        // 处理接收到的字符
        switch (received_data) {
            case '#':  // 命令开始标志
                CommandParser_Init();  // 重置状态
                command_buffer[buffer_index++] = received_data;
                parse_state = PARSE_STATE_START;
                break;
                
            case '\r':  // 回车符，忽略
                break;
                
            case '\n':  // 换行符，命令结束
                if (parse_state != PARSE_STATE_IDLE && buffer_index > 1) {
                    command_buffer[buffer_index] = '\0';  // 字符串结束符
                    
                    // 解析命令
                    CommandParser_ParseBuffer(command_buffer, &command);
                    
                    // 验证并执行命令
                    if (CommandParser_ValidateCommand(&command)) {
                        if (CommandParser_ExecuteCommand(&command)) {
                            return 1;  // 命令处理成功
                        }
                    } else {
                        CommandParser_SendResponse(RESPONSE_ERROR, "INVALID_COMMAND");
                    }
                    
                    // 重置状态
                    CommandParser_Init();
                }
                break;
                
            default:  // 普通字符
                if (parse_state != PARSE_STATE_IDLE) {
                    command_buffer[buffer_index++] = received_data;
                }
                break;
        }
    }
    
    return 0;  // 无命令处理或处理失败
}

/**
  * 函    数：发送响应
  * 参    数：status 响应状态，data 响应数据
  * 返 回 值：无
  */
void CommandParser_SendResponse(ResponseStatus_t status, const char* data)
{
    if (status == RESPONSE_OK) {
        Serial_Printf("OK:%s\r\n", data);
    } else {
        Serial_Printf("ERROR:%s\r\n", data);
    }
}

/**
  * 函    数：解析命令缓冲区
  * 参    数：buffer 命令缓冲区，cmd 解析结果存储
  * 返 回 值：无
  */
void CommandParser_ParseBuffer(const char* buffer, Command_t* cmd)
{
    char* colon_pos;
    
    // 初始化命令结构体
    memset(cmd, 0, sizeof(Command_t));
    cmd->valid = 0;
    
    // 检查命令格式（必须以#开头）
    if (buffer[0] != '#') {
        return;
    }
    
    // 查找冒号分隔符
    colon_pos = strchr(buffer + 1, ':');
    
    if (colon_pos != NULL) {
        // 有参数的命令格式：#CMD:PARAM
        int cmd_len = colon_pos - buffer - 1;
        if (cmd_len > 0 && cmd_len < sizeof(cmd->command)) {
            strncpy(cmd->command, buffer + 1, cmd_len);
            cmd->command[cmd_len] = '\0';
            
            // 复制参数
            strncpy(cmd->parameter, colon_pos + 1, sizeof(cmd->parameter) - 1);
            cmd->parameter[sizeof(cmd->parameter) - 1] = '\0';
        }
    } else {
        // 无参数的命令格式：#CMD
        strncpy(cmd->command, buffer + 1, sizeof(cmd->command) - 1);
        cmd->command[sizeof(cmd->command) - 1] = '\0';
        cmd->parameter[0] = '\0';
    }
    
    // 获取命令类型
    cmd->type = CommandParser_GetCommandType(cmd->command);
    cmd->valid = 1;
}

/**
  * 函    数：获取命令类型
  * 参    数：cmd_str 命令字符串
  * 返 回 值：命令类型
  */
CommandType_t CommandParser_GetCommandType(const char* cmd_str)
{
    if (strcmp(cmd_str, "RELAY") == 0) {
        return CMD_TYPE_RELAY;
    } else if (strcmp(cmd_str, "STATUS") == 0) {
        return CMD_TYPE_STATUS;
    } else if (strcmp(cmd_str, "MODE") == 0) {
        return CMD_TYPE_MODE;
    } else if (strcmp(cmd_str, "TEST") == 0) {
        return CMD_TYPE_TEST;
    } else if (strcmp(cmd_str, "SYSTEM") == 0) {
        return CMD_TYPE_SYSTEM;
    } else {
        return CMD_TYPE_UNKNOWN;
    }
}

/**
  * 函    数：执行命令
  * 参    数：cmd 命令结构体
  * 返 回 值：执行结果（1=成功，0=失败）
  */
uint8_t CommandParser_ExecuteCommand(const Command_t* cmd)
{
    switch (cmd->type) {
        case CMD_TYPE_RELAY:
            CommandParser_HandleRelayCommand(cmd);
            break;
            
        case CMD_TYPE_STATUS:
            CommandParser_HandleStatusCommand(cmd);
            break;
            
        case CMD_TYPE_MODE:
            CommandParser_HandleModeCommand(cmd);
            break;
            
        case CMD_TYPE_TEST:
            CommandParser_HandleTestCommand(cmd);
            break;
            
        case CMD_TYPE_SYSTEM:
            CommandParser_HandleSystemCommand(cmd);
            break;
            
        default:
            CommandParser_SendResponse(RESPONSE_ERROR, "UNKNOWN_COMMAND");
            return 0;
    }
    
    return 1;
}

/**
  * 函    数：处理继电器命令
  * 参    数：cmd 命令结构体
  * 返 回 值：无
  */
void CommandParser_HandleRelayCommand(const Command_t* cmd)
{
    char response_data[16];
    
    if (strcmp(cmd->parameter, "CH0_ON") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_0, RELAY_ON);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH0_OFF") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_0, RELAY_OFF);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH1_ON") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_1, RELAY_ON);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH1_OFF") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_1, RELAY_OFF);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH2_ON") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_2, RELAY_ON);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH2_OFF") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_2, RELAY_OFF);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH3_ON") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_3, RELAY_ON);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH3_OFF") == 0) {
        Relay_SetChannel(RELAY_CHANNEL_3, RELAY_OFF);
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "ALL_OFF") == 0) {
        Relay_AllOff();
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else {
        CommandParser_SendResponse(RESPONSE_ERROR, "INVALID_RELAY_PARAM");
    }
}

/**
  * 函    数：处理状态查询命令
  * 参    数：cmd 命令结构体
  * 返 回 值：无
  */
void CommandParser_HandleStatusCommand(const Command_t* cmd)
{
    char response_data[32];
    
    if (strcmp(cmd->parameter, "GET") == 0 || strlen(cmd->parameter) == 0) {
        sprintf(response_data, "0x%02X", Relay_GetAllStatus());
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH0") == 0) {
        sprintf(response_data, "%d", Relay_GetStatus(RELAY_CHANNEL_0));
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH1") == 0) {
        sprintf(response_data, "%d", Relay_GetStatus(RELAY_CHANNEL_1));
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH2") == 0) {
        sprintf(response_data, "%d", Relay_GetStatus(RELAY_CHANNEL_2));
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else if (strcmp(cmd->parameter, "CH3") == 0) {
        sprintf(response_data, "%d", Relay_GetStatus(RELAY_CHANNEL_3));
        CommandParser_SendResponse(RESPONSE_OK, response_data);
    } else {
        CommandParser_SendResponse(RESPONSE_ERROR, "INVALID_STATUS_PARAM");
    }
}

/**
  * 函    数：处理模式切换命令
  * 参    数：cmd 命令结构体
  * 返 回 值：无
  */
void CommandParser_HandleModeCommand(const Command_t* cmd)
{
    if (strcmp(cmd->parameter, "SINGLE") == 0) {
        Relay_SetMode(RELAY_MODE_SINGLE);
        CommandParser_SendResponse(RESPONSE_OK, "SINGLE");
    } else if (strcmp(cmd->parameter, "MULTI") == 0) {
        Relay_SetMode(RELAY_MODE_MULTI);
        CommandParser_SendResponse(RESPONSE_OK, "MULTI");
    } else if (strcmp(cmd->parameter, "GET") == 0 || strlen(cmd->parameter) == 0) {
        RelayMode_t current_mode = Relay_GetMode();
        CommandParser_SendResponse(RESPONSE_OK, (current_mode == RELAY_MODE_SINGLE) ? "SINGLE" : "MULTI");
    } else {
        CommandParser_SendResponse(RESPONSE_ERROR, "INVALID_MODE_PARAM");
    }
}

/**
  * 函    数：处理自检命令
  * 参    数：cmd 命令结构体
  * 返 回 值：无
  */
void CommandParser_HandleTestCommand(const Command_t* cmd)
{
    if (strcmp(cmd->parameter, "RELAY") == 0 || strlen(cmd->parameter) == 0) {
        uint8_t test_result = Relay_SelfTest();
        CommandParser_SendResponse(RESPONSE_OK, test_result ? "PASS" : "FAIL");
    } else {
        CommandParser_SendResponse(RESPONSE_ERROR, "INVALID_TEST_PARAM");
    }
}

/**
  * 函    数：处理系统信息命令
  * 参    数：cmd 命令结构体
  * 返 回 值：无
  */
void CommandParser_HandleSystemCommand(const Command_t* cmd)
{
    if (strcmp(cmd->parameter, "INFO") == 0 || strlen(cmd->parameter) == 0) {
        CommandParser_SendResponse(RESPONSE_OK, "STM32_RELAY_CONTROLLER_V1.0");
    } else if (strcmp(cmd->parameter, "VERSION") == 0) {
        CommandParser_SendResponse(RESPONSE_OK, "V1.0");
    } else if (strcmp(cmd->parameter, "RESET") == 0) {
        // 软件复位（可选实现）
        CommandParser_SendResponse(RESPONSE_OK, "RESET_OK");
        // NVIC_SystemReset(); // 取消注释以启用系统复位
    } else {
        CommandParser_SendResponse(RESPONSE_ERROR, "INVALID_SYSTEM_PARAM");
    }
}

/**
  * 函    数：验证命令有效性
  * 参    数：cmd 命令结构体
  * 返 回 值：验证结果（1=有效，0=无效）
  */
uint8_t CommandParser_ValidateCommand(const Command_t* cmd)
{
    // 检查命令结构体有效性
    if (!cmd->valid) {
        return 0;
    }
    
    // 检查命令字符串长度
    if (strlen(cmd->command) == 0 || strlen(cmd->command) >= sizeof(cmd->command)) {
        return 0;
    }
    
    // 检查命令类型
    if (cmd->type == CMD_TYPE_UNKNOWN) {
        return 0;
    }
    
    // 检查参数长度
    if (strlen(cmd->parameter) >= sizeof(cmd->parameter)) {
        return 0;
    }
    
    return 1;  // 命令有效
}
