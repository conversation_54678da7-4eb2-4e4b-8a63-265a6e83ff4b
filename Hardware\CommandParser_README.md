# 命令解析模块使用说明

## 概述
CommandParser模块实现ASCII命令解析和响应生成功能，支持LabVIEW上位机通过串口控制STM32四通道继电器系统。

## 命令协议规范

### 命令格式
```
#<COMMAND>:<PARAMETER>\r\n
```

### 响应格式
```
OK:<DATA>\r\n      // 成功响应
ERROR:<MESSAGE>\r\n // 错误响应
```

## 支持的命令类型

### 1. 继电器控制命令 (RELAY)

**命令格式**: `#RELAY:<PARAMETER>`

**支持的参数**:
- `CH0_ON` - 开启通道0
- `CH0_OFF` - 关闭通道0
- `CH1_ON` - 开启通道1
- `CH1_OFF` - 关闭通道1
- `CH2_ON` - 开启通道2
- `CH2_OFF` - 关闭通道2
- `CH3_ON` - 开启通道3
- `CH3_OFF` - 关闭通道3
- `ALL_OFF` - 关闭所有通道

**响应示例**:
```
命令: #RELAY:CH0_ON\r\n
响应: OK:0x01\r\n

命令: #RELAY:ALL_OFF\r\n
响应: OK:0x00\r\n
```

### 2. 状态查询命令 (STATUS)

**命令格式**: `#STATUS:<PARAMETER>`

**支持的参数**:
- `GET` 或 空参数 - 获取所有通道状态
- `CH0` - 获取通道0状态
- `CH1` - 获取通道1状态
- `CH2` - 获取通道2状态
- `CH3` - 获取通道3状态

**响应示例**:
```
命令: #STATUS:GET\r\n
响应: OK:0x0F\r\n  (所有通道开启)

命令: #STATUS:CH0\r\n
响应: OK:1\r\n     (通道0开启)
```

### 3. 模式切换命令 (MODE)

**命令格式**: `#MODE:<PARAMETER>`

**支持的参数**:
- `SINGLE` - 设置为单通道模式
- `MULTI` - 设置为多通道模式
- `GET` 或 空参数 - 获取当前模式

**响应示例**:
```
命令: #MODE:SINGLE\r\n
响应: OK:SINGLE\r\n

命令: #MODE:GET\r\n
响应: OK:MULTI\r\n
```

### 4. 自检命令 (TEST)

**命令格式**: `#TEST:<PARAMETER>`

**支持的参数**:
- `RELAY` 或 空参数 - 执行继电器自检

**响应示例**:
```
命令: #TEST:RELAY\r\n
响应: OK:PASS\r\n
```

### 5. 系统信息命令 (SYSTEM)

**命令格式**: `#SYSTEM:<PARAMETER>`

**支持的参数**:
- `INFO` 或 空参数 - 获取系统信息
- `VERSION` - 获取版本信息
- `RESET` - 软件复位（可选）

**响应示例**:
```
命令: #SYSTEM:INFO\r\n
响应: OK:STM32_RELAY_CONTROLLER_V1.0\r\n

命令: #SYSTEM:VERSION\r\n
响应: OK:V1.0\r\n
```

## 错误处理

### 常见错误响应
- `ERROR:BUFFER_OVERFLOW` - 命令缓冲区溢出
- `ERROR:INVALID_COMMAND` - 无效命令
- `ERROR:UNKNOWN_COMMAND` - 未知命令类型
- `ERROR:INVALID_RELAY_PARAM` - 无效继电器参数
- `ERROR:INVALID_STATUS_PARAM` - 无效状态查询参数
- `ERROR:INVALID_MODE_PARAM` - 无效模式参数
- `ERROR:INVALID_TEST_PARAM` - 无效测试参数
- `ERROR:INVALID_SYSTEM_PARAM` - 无效系统参数

## API接口说明

### 初始化函数
```c
void CommandParser_Init(void);
```
初始化命令解析器，清空缓冲区和状态变量。

### 命令处理函数
```c
uint8_t CommandParser_ProcessCommand(void);
```
处理串口接收到的命令，返回1表示成功处理命令，返回0表示无命令或处理失败。

### 响应发送函数
```c
void CommandParser_SendResponse(ResponseStatus_t status, const char* data);
```
发送命令响应，status为响应状态，data为响应数据。

## 使用示例

### STM32端集成示例
```c
#include "CommandParser.h"
#include "Serial.h"
#include "Relay.h"

int main(void)
{
    // 初始化模块
    Serial_Init();
    Relay_Init();
    CommandParser_Init();
    
    while (1) {
        // 处理命令
        if (CommandParser_ProcessCommand()) {
            // 命令处理成功
        }
        
        // 其他业务逻辑
        Delay_ms(10);
    }
}
```

### LabVIEW端通信示例
```
1. 配置串口：波特率9600，8位数据位，1位停止位，无校验
2. 发送命令：写入字符串 "#RELAY:CH0_ON\r\n"
3. 读取响应：读取字符串直到 "\r\n"
4. 解析响应：检查是否以 "OK:" 或 "ERROR:" 开头
```

## 配置参数

在 `Config.h` 中定义的相关参数：

```c
// 命令解析配置
#define CMD_BUFFER_SIZE        64           // 命令缓冲区大小
#define CMD_TIMEOUT_MS         1000         // 命令超时时间 1秒
#define CMD_MAX_PARAMS         8            // 最大参数数量
#define CMD_RESPONSE_DELAY     10           // 响应延时 10ms
```

## 性能特性

- **命令处理时间**: ≤10ms
- **缓冲区大小**: 64字节
- **支持命令类型**: 5种主要类型
- **参数解析**: 支持冒号分隔的参数格式
- **错误处理**: 完整的错误检测和响应机制

## 注意事项

1. **命令格式**: 必须以 `#` 开头，以 `\r\n` 结尾
2. **参数分隔**: 使用冒号 `:` 分隔命令和参数
3. **大小写敏感**: 命令和参数区分大小写
4. **缓冲区限制**: 单个命令长度不能超过64字节
5. **并发处理**: 同时只能处理一个命令
6. **超时保护**: 实现命令接收超时检测

## 扩展指南

### 添加新命令类型
1. 在 `CommandType_t` 枚举中添加新类型
2. 在 `CommandParser_GetCommandType()` 中添加识别逻辑
3. 在 `CommandParser_ExecuteCommand()` 中添加处理分支
4. 实现对应的处理函数

### 添加新参数
1. 在相应的处理函数中添加参数识别逻辑
2. 实现参数对应的功能
3. 更新文档和测试用例

## 测试验证

使用 `Test/CommandParser_Test.c` 中的测试函数验证功能：
- `Test_CommandParser_Complete()` - 完整功能测试
- `Test_Interactive_Commands()` - 交互式测试
- 各种错误情况和边界条件测试
