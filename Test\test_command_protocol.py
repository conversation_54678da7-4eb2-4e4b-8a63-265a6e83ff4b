#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32命令解析协议测试程序
功能：测试CommandParser模块的命令解析和响应功能
"""

import serial
import time
import sys

class CommandProtocolTester:
    def __init__(self, port='COM3', baudrate=9600):
        """
        初始化协议测试器
        :param port: 串口号
        :param baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.test_results = []
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=2)
            print(f"成功连接到串口 {self.port}，波特率 {self.baudrate}")
            time.sleep(2)  # 等待STM32初始化
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已断开")
    
    def send_command(self, command):
        """发送命令并接收响应"""
        if not self.ser or not self.ser.is_open:
            return None
            
        try:
            # 发送命令
            self.ser.write(command.encode('utf-8'))
            self.ser.flush()
            
            # 接收响应
            response = self.ser.readline().decode('utf-8').strip()
            return response
        except Exception as e:
            print(f"通信错误: {e}")
            return None
    
    def test_command(self, command, expected_prefix="OK", description=""):
        """测试单个命令"""
        print(f"测试: {command.strip()} - {description}")
        
        response = self.send_command(command)
        
        if response:
            print(f"响应: {response}")
            
            # 检查响应格式
            if response.startswith(expected_prefix):
                print("✓ 测试通过")
                self.test_results.append({"command": command.strip(), "result": "PASS", "response": response})
                return True
            else:
                print("✗ 测试失败 - 响应格式不正确")
                self.test_results.append({"command": command.strip(), "result": "FAIL", "response": response})
                return False
        else:
            print("✗ 测试失败 - 无响应")
            self.test_results.append({"command": command.strip(), "result": "FAIL", "response": "NO_RESPONSE"})
            return False
    
    def test_relay_commands(self):
        """测试继电器控制命令"""
        print("\n=== 测试继电器控制命令 ===")
        
        test_cases = [
            ("#RELAY:CH0_ON\r\n", "OK", "开启通道0"),
            ("#RELAY:CH1_ON\r\n", "OK", "开启通道1"),
            ("#RELAY:CH2_ON\r\n", "OK", "开启通道2"),
            ("#RELAY:CH3_ON\r\n", "OK", "开启通道3"),
            ("#RELAY:CH0_OFF\r\n", "OK", "关闭通道0"),
            ("#RELAY:CH1_OFF\r\n", "OK", "关闭通道1"),
            ("#RELAY:CH2_OFF\r\n", "OK", "关闭通道2"),
            ("#RELAY:CH3_OFF\r\n", "OK", "关闭通道3"),
            ("#RELAY:ALL_OFF\r\n", "OK", "关闭所有通道"),
        ]
        
        for command, expected, desc in test_cases:
            self.test_command(command, expected, desc)
            time.sleep(0.1)
    
    def test_status_commands(self):
        """测试状态查询命令"""
        print("\n=== 测试状态查询命令 ===")
        
        # 先设置一些继电器状态
        self.send_command("#RELAY:CH0_ON\r\n")
        self.send_command("#RELAY:CH2_ON\r\n")
        time.sleep(0.2)
        
        test_cases = [
            ("#STATUS:GET\r\n", "OK", "查询所有通道状态"),
            ("#STATUS:CH0\r\n", "OK", "查询通道0状态"),
            ("#STATUS:CH1\r\n", "OK", "查询通道1状态"),
            ("#STATUS:CH2\r\n", "OK", "查询通道2状态"),
            ("#STATUS:CH3\r\n", "OK", "查询通道3状态"),
        ]
        
        for command, expected, desc in test_cases:
            self.test_command(command, expected, desc)
            time.sleep(0.1)
    
    def test_mode_commands(self):
        """测试模式切换命令"""
        print("\n=== 测试模式切换命令 ===")
        
        test_cases = [
            ("#MODE:SINGLE\r\n", "OK", "设置单通道模式"),
            ("#MODE:GET\r\n", "OK", "查询当前模式"),
            ("#MODE:MULTI\r\n", "OK", "设置多通道模式"),
            ("#MODE:GET\r\n", "OK", "查询当前模式"),
        ]
        
        for command, expected, desc in test_cases:
            self.test_command(command, expected, desc)
            time.sleep(0.1)
    
    def test_test_commands(self):
        """测试自检命令"""
        print("\n=== 测试自检命令 ===")
        
        test_cases = [
            ("#TEST:RELAY\r\n", "OK", "继电器自检"),
        ]
        
        for command, expected, desc in test_cases:
            self.test_command(command, expected, desc)
            time.sleep(1)  # 自检需要更多时间
    
    def test_system_commands(self):
        """测试系统信息命令"""
        print("\n=== 测试系统信息命令 ===")
        
        test_cases = [
            ("#SYSTEM:INFO\r\n", "OK", "查询系统信息"),
            ("#SYSTEM:VERSION\r\n", "OK", "查询版本信息"),
        ]
        
        for command, expected, desc in test_cases:
            self.test_command(command, expected, desc)
            time.sleep(0.1)
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        test_cases = [
            ("#INVALID:CMD\r\n", "ERROR", "无效命令"),
            ("#RELAY:INVALID_PARAM\r\n", "ERROR", "无效继电器参数"),
            ("#STATUS:INVALID_PARAM\r\n", "ERROR", "无效状态参数"),
            ("#MODE:INVALID_PARAM\r\n", "ERROR", "无效模式参数"),
            ("RELAY:CH0_ON\r\n", "ERROR", "缺少命令开始符"),
        ]
        
        for command, expected, desc in test_cases:
            self.test_command(command, expected, desc)
            time.sleep(0.1)
    
    def test_performance(self):
        """测试性能"""
        print("\n=== 测试性能 ===")
        
        command = "#STATUS:GET\r\n"
        test_count = 50
        
        print(f"发送 {test_count} 个状态查询命令...")
        
        start_time = time.time()
        success_count = 0
        
        for i in range(test_count):
            response = self.send_command(command)
            if response and response.startswith("OK"):
                success_count += 1
            time.sleep(0.02)  # 20ms间隔
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"总时间: {total_time:.2f}秒")
        print(f"成功率: {success_count}/{test_count} ({success_count/test_count*100:.1f}%)")
        print(f"平均响应时间: {total_time/test_count*1000:.1f}ms")
    
    def run_all_tests(self):
        """运行所有测试"""
        if not self.connect():
            return
        
        print("STM32命令解析协议测试开始")
        print("=" * 50)
        
        try:
            # 等待系统稳定
            time.sleep(1)
            
            # 执行各项测试
            self.test_relay_commands()
            self.test_status_commands()
            self.test_mode_commands()
            self.test_test_commands()
            self.test_system_commands()
            self.test_error_handling()
            self.test_performance()
            
            # 统计测试结果
            self.print_test_summary()
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
        finally:
            self.disconnect()
    
    def print_test_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 50)
        print("测试摘要")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["result"] == "PASS"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if result["result"] == "FAIL":
                    print(f"  - {result['command']}: {result['response']}")
    
    def interactive_mode(self):
        """交互式测试模式"""
        if not self.connect():
            return
        
        print("进入交互式测试模式")
        print("输入命令进行测试，输入 'quit' 退出")
        print("示例命令: #RELAY:CH0_ON, #STATUS:GET, #MODE:SINGLE")
        
        try:
            while True:
                command = input("\n请输入命令: ").strip()
                
                if command.lower() == 'quit':
                    break
                
                if not command.endswith('\r\n'):
                    command += '\r\n'
                
                response = self.send_command(command)
                if response:
                    print(f"响应: {response}")
                else:
                    print("无响应")
        
        except KeyboardInterrupt:
            print("\n退出交互模式")
        finally:
            self.disconnect()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        port = sys.argv[1]
    else:
        port = 'COM3'  # 默认串口
    
    tester = CommandProtocolTester(port=port)
    
    print("STM32命令解析协议测试工具")
    print("1. 运行所有测试")
    print("2. 交互式测试")
    
    choice = input("请选择模式 (1/2): ").strip()
    
    if choice == '1':
        tester.run_all_tests()
    elif choice == '2':
        tester.interactive_mode()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
