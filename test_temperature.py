#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32温度监控系统 - 主控计算机端接收程序
功能：接收STM32发送的四通道温度数据并显示
"""

import serial
import time
import datetime
import csv
import os

class TemperatureMonitor:
    def __init__(self, port='COM3', baudrate=9600):
        """
        初始化温度监控系统
        :param port: 串口号
        :param baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.data_log = []
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"成功连接到串口 {self.port}，波特率 {self.baudrate}")
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已断开")
    
    def read_temperature(self):
        """读取温度数据"""
        if not self.ser or not self.ser.is_open:
            return None
            
        try:
            if self.ser.in_waiting:
                data = self.ser.readline().decode('utf-8').strip()
                if data and ',' in data:
                    temps = data.split(',')
                    if len(temps) == 4:
                        # 转换为浮点数
                        temp_values = [float(temp) for temp in temps]
                        return temp_values
        except Exception as e:
            print(f"读取数据错误: {e}")
        return None
    
    def display_temperature(self, temps):
        """显示温度数据"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n[{timestamp}] 温度数据:")
        print(f"通道0: {temps[0]:5.1f}°C")
        print(f"通道1: {temps[1]:5.1f}°C")
        print(f"通道2: {temps[2]:5.1f}°C")
        print(f"通道3: {temps[3]:5.1f}°C")
        print("-" * 40)
        
        # 保存到日志
        self.data_log.append({
            'timestamp': timestamp,
            'temp0': temps[0],
            'temp1': temps[1], 
            'temp2': temps[2],
            'temp3': temps[3]
        })
    
    def save_to_csv(self, filename='temperature_log.csv'):
        """保存数据到CSV文件"""
        if not self.data_log:
            return
            
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'temp0', 'temp1', 'temp2', 'temp3']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for row in self.data_log:
                writer.writerow(row)
        
        print(f"数据已保存到 {filename}")
    
    def run(self):
        """运行监控程序"""
        if not self.connect():
            return
        
        print("温度监控系统已启动，按Ctrl+C退出...")
        print("等待STM32数据...")
        
        try:
            while True:
                temps = self.read_temperature()
                if temps:
                    self.display_temperature(temps)
                    
                    # 检查异常温度
                    for i, temp in enumerate(temps):
                        if temp > 50.0:
                            print(f"⚠️  警告：通道{i}温度过高 ({temp:.1f}°C)")
                        elif temp < 0.0:
                            print(f"⚠️  警告：通道{i}温度异常 ({temp:.1f}°C)")
                
                time.sleep(0.1)  # 避免CPU占用过高
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行错误: {e}")
        finally:
            self.disconnect()
            if self.data_log:
                self.save_to_csv()

def main():
    """主函数"""
    print("STM32四通道温度监控系统")
    print("=" * 40)
    
    # 可以根据实际情况修改串口号
    monitor = TemperatureMonitor(port='COM3', baudrate=9600)
    monitor.run()

if __name__ == "__main__":
    main()
