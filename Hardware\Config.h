#ifndef __CONFIG_H
#define __CONFIG_H

// 系统配置参数
#define SYSTEM_CLOCK_FREQ 72000000 // 系统时钟频率 72MHz
#define ADC_REFERENCE_VOLTAGE 3.3f // ADC参考电压 3.3V
#define ADC_RESOLUTION 4095        // ADC分辨率 12位

// 温度传感器配置 (LM35)
#define TEMP_SENSOR_SENSITIVITY 0.01f // LM35灵敏度 10mV/°C
#define TEMP_CONVERSION_FACTOR 100.0f // 温度转换系数 100°C/V

// 串口配置
#define SERIAL_BAUDRATE 9600      // 串口波特率
#define SERIAL_TX_PIN GPIO_Pin_9  // 串口发送引脚 PA9
#define SERIAL_RX_PIN GPIO_Pin_10 // 串口接收引脚 PA10

// ADC通道配置
#define TEMP_CHANNEL_0 ADC_Channel_0 // 温度通道0 PA0
#define TEMP_CHANNEL_1 ADC_Channel_1 // 温度通道1 PA1
#define TEMP_CHANNEL_2 ADC_Channel_2 // 温度通道2 PA2
#define TEMP_CHANNEL_3 ADC_Channel_3 // 温度通道3 PA3

// 显示配置
#define TEMP_DISPLAY_PRECISION 1  // 温度显示精度（小数位数）
#define DATA_UPDATE_INTERVAL 1000 // 数据更新间隔 1秒

// OLED显示配置
#define OLED_SCL_PIN GPIO_Pin_8 // OLED时钟引脚 PB8
#define OLED_SDA_PIN GPIO_Pin_9 // OLED数据引脚 PB9

// 继电器控制配置
#define RELAY_PIN_0 GPIO_Pin_0 // 继电器通道0控制引脚 PC0
#define RELAY_PIN_1 GPIO_Pin_1 // 继电器通道1控制引脚 PC1
#define RELAY_PIN_2 GPIO_Pin_2 // 继电器通道2控制引脚 PC2
#define RELAY_PIN_3 GPIO_Pin_3 // 继电器通道3控制引脚 PC3
#define RELAY_SWITCH_DELAY 20  // 继电器切换延时 20ms
#define RELAY_SETTLE_TIME 50   // 继电器稳定时间 50ms

#endif
