#include "stm32f10x.h" // Device header
#include "Delay.h"
#include "OLED.h"
#include "AD.h"
#include "Serial.h"
#include "Config.h"

uint16_t AD0, AD1, AD2, AD3;	  // 定义AD值变量
float Temp0, Temp1, Temp2, Temp3; // 定义温度值变量

int main(void)
{
	/*模块初始化*/
	OLED_Init();   // OLED初始化
	AD_Init();	   // AD初始化
	Serial_Init(); // 串口初始化

	/*显示静态字符串*/
	OLED_ShowString(1, 1, "T0:");
	OLED_ShowString(2, 1, "T1:");
	OLED_ShowString(3, 1, "T2:");
	OLED_ShowString(4, 1, "T3:");

	// 发送初始化信息到串口
	Serial_SendString("STM32 Temperature Monitor Started\r\n");
	Serial_SendString("Format: T0,T1,T2,T3 (Celsius)\r\n");

	while (1)
	{
		// 获取四个通道的温度值
		Temp0 = AD_GetTemperature(TEMP_CHANNEL_0); // 获取通道0温度
		Temp1 = AD_GetTemperature(TEMP_CHANNEL_1); // 获取通道1温度
		Temp2 = AD_GetTemperature(TEMP_CHANNEL_2); // 获取通道2温度
		Temp3 = AD_GetTemperature(TEMP_CHANNEL_3); // 获取通道3温度

		// OLED显示温度值（精确到小数点后一位）
		OLED_ShowFloat(1, 4, Temp0, 2, 1); // 显示通道0温度
		OLED_ShowChar(1, 7, 'C');		   // 显示温度单位
		OLED_ShowFloat(2, 4, Temp1, 2, 1); // 显示通道1温度
		OLED_ShowChar(2, 7, 'C');		   // 显示温度单位
		OLED_ShowFloat(3, 4, Temp2, 2, 1); // 显示通道2温度
		OLED_ShowChar(3, 7, 'C');		   // 显示温度单位
		OLED_ShowFloat(4, 4, Temp3, 2, 1); // 显示通道3温度
		OLED_ShowChar(4, 7, 'C');		   // 显示温度单位

		// 通过串口发送温度数据到主控计算机
		Serial_Printf("%.1f,%.1f,%.1f,%.1f\r\n", Temp0, Temp1, Temp2, Temp3);

		Delay_ms(DATA_UPDATE_INTERVAL); // 延时，控制数据发送频率
	}
}
